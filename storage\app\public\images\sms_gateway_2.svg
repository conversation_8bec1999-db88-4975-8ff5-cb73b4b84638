<?xml version="1.0" encoding="UTF-8"?>
<svg width="800" height="600" viewBox="0 0 800 600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="headerGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#7c3aed;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ec4899;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="cardGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f8fafc;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="4" stdDeviation="8" flood-color="#000000" flood-opacity="0.1"/>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="800" height="600" fill="#f8fafc"/>
  
  <!-- Header -->
  <rect width="800" height="70" fill="url(#headerGradient)"/>
  <text x="30" y="30" font-family="Segoe UI, Arial, sans-serif" font-size="22" font-weight="600" fill="white">SMS Gateway - Advanced Settings</text>
  <text x="30" y="50" font-family="Segoe UI, Arial, sans-serif" font-size="14" fill="rgba(255,255,255,0.8)">Configure message templates and delivery options</text>
  
  <!-- Breadcrumb -->
  <rect x="0" y="70" width="800" height="40" fill="white" stroke="#e5e7eb" stroke-width="1"/>
  <text x="30" y="92" font-family="Segoe UI, Arial, sans-serif" font-size="14" fill="#6b7280">Settings > Notifications > SMS Gateway > Advanced</text>
  
  <!-- Main Content -->
  <rect x="30" y="130" width="740" height="450" fill="white" filter="url(#shadow)" rx="8"/>
  
  <!-- Form Title -->
  <text x="50" y="165" font-family="Segoe UI, Arial, sans-serif" font-size="24" font-weight="700" fill="#111827">Message Templates & Settings</text>
  <text x="50" y="185" font-family="Segoe UI, Arial, sans-serif" font-size="14" fill="#6b7280">Customize your SMS message templates and delivery preferences</text>
  
  <!-- Message Template Section -->
  <rect x="50" y="200" width="690" height="140" fill="#f9fafb" stroke="#e5e7eb" stroke-width="1" rx="6"/>
  <text x="70" y="225" font-family="Segoe UI, Arial, sans-serif" font-size="16" font-weight="600" fill="#374151">Default Message Template</text>
  <text x="70" y="245" font-family="Segoe UI, Arial, sans-serif" font-size="14" fill="#6b7280">Template for notification messages</text>
  
  <rect x="70" y="255" width="650" height="70" fill="white" stroke="#d1d5db" stroke-width="1" rx="4"/>
  <text x="80" y="275" font-family="Segoe UI, Arial, sans-serif" font-size="14" fill="#374151">Hello {{name}},</text>
  <text x="80" y="295" font-family="Segoe UI, Arial, sans-serif" font-size="14" fill="#374151">You have a new notification: {{message}}</text>
  <text x="80" y="315" font-family="Segoe UI, Arial, sans-serif" font-size="14" fill="#374151">Best regards, {{company_name}}</text>
  
  <!-- Delivery Settings -->
  <text x="50" y="370" font-family="Segoe UI, Arial, sans-serif" font-size="18" font-weight="600" fill="#374151">Delivery Settings</text>
  
  <!-- Retry Attempts -->
  <text x="50" y="400" font-family="Segoe UI, Arial, sans-serif" font-size="14" font-weight="600" fill="#374151">Retry Attempts</text>
  <rect x="50" y="410" width="150" height="35" fill="#f9fafb" stroke="#d1d5db" stroke-width="1" rx="6"/>
  <text x="60" y="430" font-family="Segoe UI, Arial, sans-serif" font-size="14" fill="#6b7280">3</text>
  
  <!-- Retry Delay -->
  <text x="220" y="400" font-family="Segoe UI, Arial, sans-serif" font-size="14" font-weight="600" fill="#374151">Retry Delay (minutes)</text>
  <rect x="220" y="410" width="150" height="35" fill="#f9fafb" stroke="#d1d5db" stroke-width="1" rx="6"/>
  <text x="230" y="430" font-family="Segoe UI, Arial, sans-serif" font-size="14" fill="#6b7280">5</text>
  
  <!-- Message Priority -->
  <text x="390" y="400" font-family="Segoe UI, Arial, sans-serif" font-size="14" font-weight="600" fill="#374151">Message Priority</text>
  <rect x="390" y="410" width="150" height="35" fill="#f9fafb" stroke="#d1d5db" stroke-width="1" rx="6"/>
  <text x="400" y="430" font-family="Segoe UI, Arial, sans-serif" font-size="14" fill="#6b7280">Normal</text>
  
  <!-- Notification Types -->
  <text x="50" y="480" font-family="Segoe UI, Arial, sans-serif" font-size="18" font-weight="600" fill="#374151">Notification Types</text>
  
  <!-- Checkboxes for notification types -->
  <rect x="50" y="495" width="16" height="16" fill="#10b981" stroke="#10b981" stroke-width="1" rx="2"/>
  <text x="55" y="505" font-family="Segoe UI, Arial, sans-serif" font-size="12" font-weight="600" fill="white">✓</text>
  <text x="75" y="507" font-family="Segoe UI, Arial, sans-serif" font-size="14" fill="#374151">Task Assignments</text>
  
  <rect x="200" y="495" width="16" height="16" fill="#10b981" stroke="#10b981" stroke-width="1" rx="2"/>
  <text x="205" y="505" font-family="Segoe UI, Arial, sans-serif" font-size="12" font-weight="600" fill="white">✓</text>
  <text x="225" y="507" font-family="Segoe UI, Arial, sans-serif" font-size="14" fill="#374151">Project Updates</text>
  
  <rect x="350" y="495" width="16" height="16" fill="white" stroke="#d1d5db" stroke-width="1" rx="2"/>
  <text x="375" y="507" font-family="Segoe UI, Arial, sans-serif" font-size="14" fill="#374151">Meeting Reminders</text>
  
  <rect x="500" y="495" width="16" height="16" fill="#10b981" stroke="#10b981" stroke-width="1" rx="2"/>
  <text x="505" y="505" font-family="Segoe UI, Arial, sans-serif" font-size="12" font-weight="600" fill="white">✓</text>
  <text x="525" y="507" font-family="Segoe UI, Arial, sans-serif" font-size="14" fill="#374151">Deadline Alerts</text>
  
  <!-- Second row of checkboxes -->
  <rect x="50" y="520" width="16" height="16" fill="white" stroke="#d1d5db" stroke-width="1" rx="2"/>
  <text x="75" y="532" font-family="Segoe UI, Arial, sans-serif" font-size="14" fill="#374151">System Maintenance</text>
  
  <rect x="200" y="520" width="16" height="16" fill="#10b981" stroke="#10b981" stroke-width="1" rx="2"/>
  <text x="205" y="530" font-family="Segoe UI, Arial, sans-serif" font-size="12" font-weight="600" fill="white">✓</text>
  <text x="225" y="532" font-family="Segoe UI, Arial, sans-serif" font-size="14" fill="#374151">User Invitations</text>
  
  <rect x="350" y="520" width="16" height="16" fill="#10b981" stroke="#10b981" stroke-width="1" rx="2"/>
  <text x="355" y="530" font-family="Segoe UI, Arial, sans-serif" font-size="12" font-weight="600" fill="white">✓</text>
  <text x="375" y="532" font-family="Segoe UI, Arial, sans-serif" font-size="14" fill="#374151">Status Changes</text>
  
  <!-- Action Buttons -->
  <rect x="50" y="550" width="140" height="40" fill="#7c3aed" rx="6"/>
  <text x="105" y="573" font-family="Segoe UI, Arial, sans-serif" font-size="14" font-weight="600" fill="white">Save Configuration</text>
  
  <rect x="200" y="550" width="120" height="40" fill="white" stroke="#d1d5db" stroke-width="1" rx="6"/>
  <text x="245" y="573" font-family="Segoe UI, Arial, sans-serif" font-size="14" font-weight="500" fill="#6b7280">Reset to Default</text>
  
  <rect x="330" y="550" width="100" height="40" fill="#f59e0b" rx="6"/>
  <text x="365" y="573" font-family="Segoe UI, Arial, sans-serif" font-size="14" font-weight="500" fill="white">Test Template</text>
  
  <!-- Status Messages -->
  <rect x="550" y="495" width="190" height="60" fill="#ecfdf5" stroke="#10b981" stroke-width="1" rx="6"/>
  <text x="570" y="515" font-family="Segoe UI, Arial, sans-serif" font-size="14" font-weight="600" fill="#065f46">✓ Configuration Valid</text>
  <text x="570" y="535" font-family="Segoe UI, Arial, sans-serif" font-size="12" fill="#047857">All settings are properly configured</text>
  <text x="570" y="550" font-family="Segoe UI, Arial, sans-serif" font-size="12" fill="#047857">Last updated: 2 minutes ago</text>
</svg>
