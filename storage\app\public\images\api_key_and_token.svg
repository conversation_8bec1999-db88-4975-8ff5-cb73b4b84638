<?xml version="1.0" encoding="UTF-8"?>
<svg width="800" height="600" viewBox="0 0 800 600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="headerGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="cardGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f8fafc;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="4" stdDeviation="8" flood-color="#000000" flood-opacity="0.1"/>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="800" height="600" fill="#f1f5f9"/>
  
  <!-- Header -->
  <rect width="800" height="80" fill="url(#headerGradient)"/>
  <text x="40" y="35" font-family="Segoe UI, Arial, sans-serif" font-size="24" font-weight="600" fill="white">SMS Gateway Dashboard</text>
  <text x="40" y="60" font-family="Segoe UI, Arial, sans-serif" font-size="14" fill="rgba(255,255,255,0.8)">API Keys & Credentials</text>
  
  <!-- Navigation -->
  <rect x="0" y="80" width="200" height="520" fill="#2d3748"/>
  <text x="20" y="110" font-family="Segoe UI, Arial, sans-serif" font-size="16" font-weight="500" fill="white">Navigation</text>
  <rect x="10" y="130" width="180" height="35" rx="4" fill="#4a5568"/>
  <text x="20" y="150" font-family="Segoe UI, Arial, sans-serif" font-size="14" fill="white">📊 Dashboard</text>
  <rect x="10" y="175" width="180" height="35" rx="4" fill="#667eea"/>
  <text x="20" y="195" font-family="Segoe UI, Arial, sans-serif" font-size="14" fill="white">🔑 API Keys</text>
  <text x="20" y="230" font-family="Segoe UI, Arial, sans-serif" font-size="14" fill="#a0aec0">📱 Messaging</text>
  <text x="20" y="260" font-family="Segoe UI, Arial, sans-serif" font-size="14" fill="#a0aec0">📈 Analytics</text>
  <text x="20" y="290" font-family="Segoe UI, Arial, sans-serif" font-size="14" fill="#a0aec0">⚙️ Settings</text>
  
  <!-- Main Content Area -->
  <rect x="220" y="100" width="560" height="480" fill="white" filter="url(#shadow)" rx="8"/>
  
  <!-- Page Title -->
  <text x="240" y="140" font-family="Segoe UI, Arial, sans-serif" font-size="28" font-weight="700" fill="#1a202c">API Keys & Credentials</text>
  <text x="240" y="165" font-family="Segoe UI, Arial, sans-serif" font-size="16" fill="#718096">Manage your API authentication credentials</text>
  
  <!-- API Key Card -->
  <rect x="240" y="190" width="520" height="160" fill="url(#cardGradient)" stroke="#e2e8f0" stroke-width="1" rx="8"/>
  <text x="260" y="220" font-family="Segoe UI, Arial, sans-serif" font-size="18" font-weight="600" fill="#2d3748">Account SID</text>
  <rect x="260" y="230" width="480" height="40" fill="#f7fafc" stroke="#cbd5e0" stroke-width="1" rx="4"/>
  <text x="270" y="252" font-family="Monaco, Consolas, monospace" font-size="14" fill="#4a5568">AC1234567890abcdef1234567890abcdef</text>
  <rect x="680" y="235" width="60" height="30" fill="#667eea" rx="4"/>
  <text x="695" y="252" font-family="Segoe UI, Arial, sans-serif" font-size="12" font-weight="500" fill="white">Copy</text>
  
  <text x="260" y="295" font-family="Segoe UI, Arial, sans-serif" font-size="18" font-weight="600" fill="#2d3748">Auth Token</text>
  <rect x="260" y="305" width="480" height="40" fill="#f7fafc" stroke="#cbd5e0" stroke-width="1" rx="4"/>
  <text x="270" y="327" font-family="Monaco, Consolas, monospace" font-size="14" fill="#4a5568">••••••••••••••••••••••••••••••••</text>
  <rect x="680" y="310" width="60" height="30" fill="#667eea" rx="4"/>
  <text x="695" y="327" font-family="Segoe UI, Arial, sans-serif" font-size="12" font-weight="500" fill="white">Show</text>
  
  <!-- API Endpoint Card -->
  <rect x="240" y="370" width="520" height="120" fill="url(#cardGradient)" stroke="#e2e8f0" stroke-width="1" rx="8"/>
  <text x="260" y="400" font-family="Segoe UI, Arial, sans-serif" font-size="18" font-weight="600" fill="#2d3748">API Base URL</text>
  <text x="260" y="420" font-family="Segoe UI, Arial, sans-serif" font-size="14" fill="#718096">Use this endpoint for sending SMS messages</text>
  <rect x="260" y="430" width="480" height="40" fill="#f7fafc" stroke="#cbd5e0" stroke-width="1" rx="4"/>
  <text x="270" y="452" font-family="Monaco, Consolas, monospace" font-size="14" fill="#4a5568">https://api.smsgateway.com/v1/messages</text>
  <rect x="680" y="435" width="60" height="30" fill="#667eea" rx="4"/>
  <text x="695" y="452" font-family="Segoe UI, Arial, sans-serif" font-size="12" font-weight="500" fill="white">Copy</text>
  
  <!-- Status Indicators -->
  <circle cx="720" cy="210" r="6" fill="#48bb78"/>
  <text x="735" y="215" font-family="Segoe UI, Arial, sans-serif" font-size="12" fill="#48bb78">Active</text>
  
  <!-- Instructions -->
  <rect x="240" y="510" width="520" height="60" fill="#edf2f7" stroke="#cbd5e0" stroke-width="1" rx="6"/>
  <text x="260" y="530" font-family="Segoe UI, Arial, sans-serif" font-size="14" font-weight="600" fill="#2d3748">💡 Quick Setup</text>
  <text x="260" y="550" font-family="Segoe UI, Arial, sans-serif" font-size="12" fill="#4a5568">Copy your Account SID and Auth Token to configure your application</text>
  <text x="260" y="565" font-family="Segoe UI, Arial, sans-serif" font-size="12" fill="#4a5568">Use the API Base URL for sending SMS messages via our REST API</text>
</svg>
